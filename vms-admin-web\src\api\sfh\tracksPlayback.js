import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/sfh/track/` + url, ...arg)

export default {
  // 获取调运单每日数量统计列表
  dailySummary(data) {
    return request('daily/summary', data, 'get')
  },
  // 获取调运单单日列表
  getTransportApplicationList(data) {
    return request('transport/application/list', data, 'get')
  },
  // 获取调运单关联任务列表
  getTransportTaskList(data) {
    return request('transport/task/list', data, 'get')
  },
  // 获取调运任务轨迹
  getLocationDetail(data) {
    return request('location/detail', data, 'get')
  },
  // 获取调运任务报警列表
  getAlarmRecordDetail(data) {
    return request('alarmRecord/detail', data, 'get')
  }
}
