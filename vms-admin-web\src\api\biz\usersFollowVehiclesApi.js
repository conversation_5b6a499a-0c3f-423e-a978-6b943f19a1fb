import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/vehiclewatchlist/` + url, ...arg)

/**
 * 用户关注车辆
 *
 * <AUTHOR>
 * @date  2024/08/08
 **/
export default {
  // 获取用户关注车辆列表
  list(data) {
    return request('list', data, 'get')
  },

  // 添加用户关注车辆
  add(data) {
    return request('add', data, 'post')
  },

  // 删除用户关注车辆
  delete(data) {
    return request('delete', data, 'post')
  }
}
