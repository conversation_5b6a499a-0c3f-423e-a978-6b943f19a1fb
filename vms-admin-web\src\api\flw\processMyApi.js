import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/flw/process/` + url, ...arg)
/**
 * 我的流程
 *
 * <AUTHOR>
 * @date 2022-09-22 22:33:20
 */
export default {
  // 获取我可以发起的流程模型列表
  processMyModelList(data) {
    return request('myModelList', data, 'get')
  },
  // 保存草稿
  processSaveDraft(data) {
    return request('saveDraft', data)
  },
  // 发起流程
  processStart(data) {
    return request('start', data)
  },
  // 获取我的草稿分页
  processMyDraftPage(data) {
    return request('myDraftPage', data, 'get')
  },
  // 获取草稿详情
  processDraftDetail(data) {
    return request('draftDetail', data, 'get')
  },
  // 删除草稿
  processDeleteDraft(data) {
    return request('deleteDraft', data)
  },
  // 获取我发起的流程分页
  processMyPage(data) {
    return request('myPage', data, 'get')
  },
  // 获取我的待阅流程分页
  processMyCopyUnreadPage(data) {
    return request('myCopyUnreadPage', data, 'get')
  },
  // 设置待阅流程为已阅
  processReadMyCopyProcess(data) {
    return request('readMyCopyProcess', data)
  },
  // 获取我的已阅流程分页
  processMyCopyHasReadPage(data) {
    return request('myCopyHasReadPage', data, 'get')
  },
  // 删除我的已阅流程
  processDeleteMyHasReadProcess(data) {
    return request('deleteMyHasReadProcess', data)
  },
  // 撤回流程
  processRevoke(data) {
    return request('revoke', data)
  },
  // 获取流程详情
  processDetail(data) {
    return request('detail', data, 'get')
  },
  // 获取流程模型详情
  processModelDetail(data) {
    return request('modelDetail', data, 'get')
  }
}
