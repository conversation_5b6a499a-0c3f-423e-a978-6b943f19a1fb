import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/vehiclerepair/` + url, ...arg)

/**
 * 车辆维修Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/29 14:31
 **/
export default {
  // 获取车辆维修分页
  vehicleRepairPage(data) {
    return request('page', data, 'get')
  },
  // 提交车辆维修表单 edit为true时为编辑，默认为新增
  vehicleRepairSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除车辆维修
  vehicleRepairDelete(data) {
    return request('delete', data)
  },
  // 获取车辆维修详情
  vehicleRepairDetail(data) {
    return request('detail', data, 'get')
  },
  // 导出车辆维修列表
  export(data) {
    return request('export', data, 'get', {
      responseType: 'blob'
    })
  }
}
