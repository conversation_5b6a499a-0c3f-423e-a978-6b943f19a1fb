import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/vehiclefuelrecord/` + url, ...arg)

/**
 * 车辆加油Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/29 14:29
 **/
export default {
  // 获取车辆加油分页
  vehicleFuelRecordPage(data) {
    return request('page', data, 'get')
  },
  // 提交车辆加油表单 edit为true时为编辑，默认为新增
  vehicleFuelRecordSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除车辆加油
  vehicleFuelRecordDelete(data) {
    return request('delete', data)
  },
  // 获取车辆加油详情
  vehicleFuelRecordDetail(data) {
    return request('detail', data, 'get')
  },
  // 导出车辆加油列表
  export(data) {
    return request('export', data, 'get', {
      responseType: 'blob'
    })
  }
}
