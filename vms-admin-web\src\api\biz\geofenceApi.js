import {
  baseRequest
} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/geofence/` + url, ...arg)

/**
 * 电子围栏Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/07/02 10:02
 **/
export default {
  // 获取电子围栏分页
  geofencePage(data) {
    return request('page', data, 'get')
  },
  // 获取电子围栏列表
  geofenceList(data) {
    return request('list', data, 'get')
  },
  // 提交电子围栏表单 edit为true时为编辑，默认为新增
  geofenceSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除电子围栏
  geofenceDelete(data) {
    return request('delete', data)
  },
  // 获取电子围栏详情
  geofenceDetail(data) {
    return request('detail', data, 'get')
  },
  // 绑定电子围栏设备
  geofenceBindRelation(data) {
    return request('bindRelation', data, 'post')
  },
  // 生成线路电子围栏区域
  polylineArea(data) {
    return request('polylineArea', data, 'post')
  },
  //批量创建电子围栏
  addBatch(data) {
    return request('addBatch', data, 'post')
  }
}
