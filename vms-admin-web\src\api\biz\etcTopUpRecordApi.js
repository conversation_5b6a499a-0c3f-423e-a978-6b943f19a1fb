import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/etctopuprecord/` + url, ...arg)

/**
 * ETC充值记录表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/09/26 15:35
 **/
export default {
  // 获取ETC充值记录表分页
  etcTopUpRecordPage(data) {
    return request('page', data, 'get')
  },
  // 提交ETC充值记录表表单 edit为true时为编辑，默认为新增
  etcTopUpRecordSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除ETC充值记录表
  etcTopUpRecordDelete(data) {
    return request('delete', data)
  },
  // 获取ETC充值记录表详情
  etcTopUpRecordDetail(data) {
    return request('detail', data, 'get')
  },
  // 导出ETC充值记录列表
  export(data) {
    return request('export', data, 'get', {
      responseType: 'blob'
    })
  }
}
