import { baseRequest } from '@/utils/request'

const realtimeVideoRequest = (url, ...arg) => baseRequest(`/video/realtime/` + url, ...arg)
const historyVideoRequest = (url, ...arg) => baseRequest(`/video/history/` + url, ...arg)
const scheduleVideoRequest = (url, ...arg) => baseRequest(`/video/schedule/` + url, ...arg)

export default {
  // 实时视频相关接口
  startRealtimeVideo(data) {
    return realtimeVideoRequest('start', data, 'post')
  },
  // 实时视频心跳上报
  realtimeVideoHeartbeat(data) {
    return realtimeVideoRequest('heartbeat', data, 'post')
  },
  // 结束实时视频
  endRealtimeVideo(data) {
    return realtimeVideoRequest('end', data, 'post')
  },
  // 查询历史视频资源
  searchHistoryVideo(data) {
    return historyVideoRequest('search', data, 'post')
  },
  // 播放历史视频
  playHistoryVideo(data) {
    return historyVideoRequest('play', data, 'post')
  },
  // 历史视频心跳
  historyVideoHeartbeat(data) {
    return historyVideoRequest('heartbeat', data, 'post')
  },
  // 历史视频控制
  controlHistoryVideo(data) {
    return historyVideoRequest('control', data, 'post')
  },
  // 查询视频日程统计
  searchVideoSchedule(data) {
    return scheduleVideoRequest('search', data, 'post')
  }
}
