import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/vehicletrafficviolation/` + url, ...arg)

/**
 * 车辆违章Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/29 14:31
 **/
export default {
  // 获取车辆违章分页
  vehicleTrafficViolationPage(data) {
    return request('page', data, 'get')
  },
  // 提交车辆违章表单 edit为true时为编辑，默认为新增
  vehicleTrafficViolationSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除车辆违章
  vehicleTrafficViolationDelete(data) {
    return request('delete', data)
  },
  // 获取车辆违章详情
  vehicleTrafficViolationDetail(data) {
    return request('detail', data, 'get')
  },
  // 导出车辆违章列表
  export(data) {
    return request('export', data, 'get', {
      responseType: 'blob'
    })
  }
}
