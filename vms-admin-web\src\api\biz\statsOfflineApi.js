import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/statsoffline/` + url, ...arg)

/**
 * 设备离线记录表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/19 15:35
 **/
export default {
  // 获取设备离线记录表分页
  statsOfflinePage(data) {
    return request('page', data, 'get')
  },
  // 提交设备离线记录表表单 edit为true时为编辑，默认为新增
  statsOfflineSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除设备离线记录表
  statsOfflineDelete(data) {
    return request('delete', data)
  },
  // 获取设备离线记录表详情
  statsOfflineDetail(data) {
    return request('detail', data, 'get')
  }
}
