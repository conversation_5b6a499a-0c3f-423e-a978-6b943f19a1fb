import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/sfh/transportapplication/` + url, ...arg)

/**
 * 调运申报Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/12/04 10:32
 **/
export default {
  // 获取调运申报分页
  transportApplicationPage(data) {
    return request('page', data, 'get')
  },
  // 新增调运申报表单
  transportApplicationSubmitForm(data) {
    return request('add', data)
  },
  // 保存/提交调运申报表单
  transportApplicationEdit(data) {
    return request('edit', data)
  },
  // 删除调运申报
  transportApplicationDelete(data) {
    return request('delete', data)
  },
  // 获取调运申报详情
  transportApplicationDetail(data) {
    return request('detail', data, 'get')
  },
  // 撤回调运申报
  transportApplicationRetract(data) {
    return request('retract', data, 'post')
  },
  // 调运路线规划
  transportApplicationRoutePlanning(data) {
    return request('routePlanning', data, 'get')
  },
  // 查询调运预计路线
  transportApplicationGetPlanedTransportPath(data) {
    return request('getPlanedTransportPath', data, 'get')
  }
}
