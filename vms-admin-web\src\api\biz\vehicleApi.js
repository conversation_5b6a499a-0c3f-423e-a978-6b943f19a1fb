import {
  baseRequest
} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/vehicle/` + url, ...arg)

/**
 * 车辆信息Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/29 14:29
 **/
export default {
  // 获取车辆信息分页
  vehiclePage(data) {
    return request('page', data, 'get')
  },
  // 获取车辆信息集合
  vehicleList(data) {
    return request('list', data, 'get')
  },
  // 提交车辆信息表单 edit为true时为编辑，默认为新增
  vehicleSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除车辆信息
  vehicleDelete(data) {
    return request('delete', data)
  },
  // 获取车辆信息详情
  vehicleDetail(data) {
    return request('detail', data, 'get')
  },
  // 车辆绑定设备
  vehicleBindDevice(data) {
    return request('bindDevice', data, 'post')
  },
  // 车辆解绑设备
  vehicleUnbindDevice(data) {
    return request('unbindDevice', data, 'post')
  },
  // 根据id集合获取车辆集合
  getVehicleListByIdList(data) {
    return request('getVehicleListByIdList', data)
  },
  // 车辆批量转移组织
  batchMoveOrg(data) {
    return request('batchMoveOrg', data, 'post')
  },
  //下载批量绑定模板
  downloadImportBindTemplate(data) {
    return request('downloadImportBindTemplate', data, 'get', {
      responseType: 'blob'
    })
  },
  //批量绑定
  batchVehicleBindDevice(data) {
    return request('batchVehicleBindDevice', data, 'post')
  },
  //下载车辆导入模板
  downloadImportVehicleTemplate(data) {
    return request('downloadImportVehicleTemplate', data, 'get', {
      responseType: 'blob'
    })
  },
  //车辆导入
  importVehicle(data) {
    return request('import', data, 'post')
  },
  // 获取所有车辆实时位置
  getVehicleLocations(data) {
    return request('location', data, 'get')
  },
  // 车辆导出
  export (data) {
    return request('export', data, 'get', {
      responseType: 'blob'
    })
  },
  // 获取车辆绑定历史
  getVehicleBindingHistory(data) {
    return request('bindHistory', data, 'get')
  },

  // 获取车辆停靠信息分页
  vehicleParkingRecordPage(data) {
    return request('parkingRecord', data, 'get')
  },
  //根据日期获取车辆轨迹和停靠记录
  locationAndParkingRecord(data) {
    return request('locationAndParkingRecord', data, 'get')
  }
}
