import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/sfh/enterprisevehicleregistration/` + url, ...arg)

/**
 * 进境粮食企业车辆信息备案表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/12/06 14:01
 **/
export default {
  // 获取进境粮食企业车辆信息备案表分页
  enterpriseVehicleRegistrationPage(data) {
    return request('page', data, 'get')
  },
  // 提交进境粮食企业车辆信息备案表表单 edit为true时为编辑，默认为新增
  enterpriseVehicleRegistrationSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除进境粮食企业车辆信息备案表
  enterpriseVehicleRegistrationDelete(data) {
    return request('delete', data)
  },
  // 获取进境粮食企业车辆信息备案表详情
  enterpriseVehicleRegistrationDetail(data) {
    return request('detail', data, 'get')
  },
  // 撤回进境粮食企业车辆信息备案
  enterpriseVehicleRegistrationWithdraw(data) {
    return request('withdrawal', data, 'post')
  },
  // 提交进境粮食企业车辆信息备案表
  enterpriseVehicleRegistrationSubmit(data) {
    return request('submit', data, 'post')
  },
  // 保存进境粮食企业车辆信息备案表
  enterpriseVehicleRegistrationSave(data) {
    return request('save', data, 'post')
  },
  // 通过车牌号获取车辆信息
  enterpriseVehicleRegistrationVehicleDetail(data) {
    return request('vehicle', data, 'get')
  },
  // 审核进境粮食企业车辆信息备案
  enterpriseVehicleRegistrationAudit(data) {
    return request('audit', data, 'post')
  },
  // 校验企业车辆是否备案
  enterpriseVehicleRegistrationValidate(data) {
    return request('validate', data, 'get')
  },
  // 获取进境粮食企业车辆信息备案列表
  enterpriseVehicleRegistrationList(data) {
    return request('list', data, 'get')
  },
  // 获取进境粮食企业车辆信息备案历史列表
  enterpriseVehicleRegistrationHistory(data) {
    return request('history', data, 'get')
  },
}
