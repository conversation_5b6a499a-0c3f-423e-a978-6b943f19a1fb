import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/wechat/` + url, ...arg)

/**
 * 微信相关接口管理器
 *
 * <AUTHOR>
 * @date 2023/07/21
 **/
export default {
  // 获取微信绑定二维码
  getQrCode() {
    return request('qrcode', {}, 'get')
  },

  // 检查微信绑定状态
  checkBindStatus() {
    return request('bind/status', {}, 'get')
  },

  // 解绑微信
  unbindWechat() {
    return request('unbind', {})
  }
}
