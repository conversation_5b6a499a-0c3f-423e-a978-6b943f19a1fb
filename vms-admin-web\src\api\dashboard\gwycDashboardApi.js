import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/dashboard/` + url, ...arg)
/**
 * 公务用车大屏接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/26
 **/
export default {
  // 车辆人员统计
  statistics(data) {
    return request('vehicleDriver/statistics', data, 'get')
  },

  // 当前车辆动态
  vehicleDynamic(data) {
    return request('vehicle/dynamic', data, 'get')
  },

  // 车辆出车排名
  vehicleUse(data) {
    return request('top/vehicleUse', data, 'get')
  },

  // 用车部门排名
  vehicleUseOrg(data) {
    return request('top/vehicleUseOrg', data, 'get')
  },

  // 驾驶员出车排行
  vehicleDriver(data) {
    return request('top/vehicleDriver', data, 'get')
  },

  // 车辆费用占比排名
  vehicleCostProportion(data) {
    return request('top/vehicleCostProportion', data, 'get')
  },

  // 当前司机动态
  driverDynamic(data) {
    return request('driver/dynamic', data, 'get')
  },

  //地图上展示的车辆集合
  mapVehicleList(data) {
    return baseRequest('/biz/location/getVehicleLocations', data, 'post')
  }
}
