import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/flw/model/` + url, ...arg)
/**
 * 模型
 *
 * <AUTHOR>
 * @date 2022-09-22 22:33:20
 */
export default {
  // 获取模型分页
  modelPage(data) {
    return request('page', data, 'get')
  },
  // 获取所有模型列表
  modelAllList(data) {
    return request('allList', data, 'get')
  },
  // 提交表单 edit为true时为编辑，默认为新增
  submitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除模型
  modelDelete(data) {
    return request('delete', data)
  },
  // 部署模型
  modelDeploy(data) {
    return request('deploy', data)
  },
  // 获取模型详情
  modelDetail(data) {
    return request('detail', data, 'get')
  },
  // 停用模型
  modelDisable(data) {
    return request('disableModel', data)
  },
  // 启用模型
  modelEnable(data) {
    return request('enableModel', data)
  },
  // 模型降版
  modelDownVersion(data) {
    return request('downVersion', data)
  },
  // 获取组织树选择器
  modelOrgTreeSelector(data) {
    return request('orgTreeSelector', data, 'get')
  },
  // 获取组织列表选择器
  modelOrgListSelector(data) {
    return request('orgListSelector', data, 'get')
  },
  // 获取职位选择器
  modelPositionSelector(data) {
    return request('positionSelector', data, 'get')
  },
  // 获取角色选择器
  modelRoleSelector(data) {
    return request('roleSelector', data, 'get')
  },
  // 获取用户选择器
  modelUserSelector(data) {
    return request('userSelector', data, 'get')
  },
  // 获取司机选择器
  modelDriverSelector(data) {
    return request('driverSelector', data, 'get')
  },
  // 获取车辆选择器
  modelVehicleSelector(data) {
    return request('vehicleSelector', data, 'get')
  },
  // 获取执行监听器选择器
  modelExecutionListenerSelector(data) {
    return request('executionListenerSelector', data, 'get')
  },
  // 获取自定义事件执行监听器选择器
  modelExecutionListenerSelectorForCustomEvent(data) {
    return request('executionListenerSelectorForCustomEvent', data, 'get')
  },
  // 获取任务监听器选择器
  modelTaskListenerSelector(data) {
    return request('taskListenerSelector', data, 'get')
  },
  // 获取流程模型选择器
  modelSelector(data) {
    return request('modelSelector', data, 'get')
  },
  // 通过ID集合获取模型详情
  modelDetailByIdList(data) {
    return request('detailByIdList', data)
  },
  // 获取自定义参与人提供者选择器
  modelParticipateProviderSelectorForCustom(data) {
    return request('participateProviderSelectorForCustom', data, 'get')
  }
}
