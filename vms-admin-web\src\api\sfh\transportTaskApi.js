import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/sfh/transportTask/` + url, ...arg)

export default {
  // 获取调运运输任务表分页
  transportTaskPage(data) {
    return request('page', data, 'get')
  },

  // 获取调运运输任务表详情
  transportTaskDetail(data) {
    return request('detail', data, 'get')
  },

  //  海关任务审批
  transportTaskCustomAudit(data) {
    return request('custom/audit', data, 'post')
  },
  // 任务审核分页
  transportTaskCustomPage(data) {
    return request('custom/page', data, 'get')
  }
}
