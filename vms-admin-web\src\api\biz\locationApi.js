import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/location/` + url, ...arg)

/**
 * 设备定位数据Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/11/07 13:33
 **/
export default {
  // 获取设备定位数据分页
  locationPage(data) {
    return request('page', data, 'get')
  },
  exportLocation(data) {
    return request('export', data, 'get', { responseType: 'blob' })
  },
  // 提交设备定位数据表单 edit为true时为编辑，默认为新增
  locationSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 获取设备定位数据详情
  locationDetail(data) {
    return request('detail', data, 'get')
  },
  // 获取车辆位置信息
  getVehicleLocations(data) {
    return request('getVehicleLocations', data, 'post')
  }
}
