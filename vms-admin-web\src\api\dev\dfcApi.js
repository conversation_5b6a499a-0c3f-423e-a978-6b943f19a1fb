import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/dev/dfc/` + url, ...arg)

/**
 * 动态字段配置Api接口管理器
 *
 * <AUTHOR>
 * @date  2023/08/04 08:18
 **/
export default {
  // 获取动态字段配置分页
  dfcPage(data) {
    return request('page', data, 'get')
  },
  // 提交动态字段配置表单 edit为true时为编辑，默认为新增
  dfcSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除动态字段配置
  dfcDelete(data) {
    return request('delete', data)
  },
  // 获取动态字段配置详情
  dfcDetail(data) {
    return request('detail', data, 'get')
  },
  // 迁移数据
  dfcMigrate(data) {
    return request('migrate', data)
  },

  /* ====动态字段部分所需要用到的选择器==== */

  // 获取所有数据源信息
  dfcDbsSelector(data) {
    return request('dbsSelector', data, 'get')
  },
  // 获取对应库所有表信息
  dbTableSelector(data) {
    return request('dbTableSelector', data, 'get')
  },
  // 获取对应库数据表内所有字段信息
  dbColumnSelector(data) {
    return request('dbColumnSelector', data, 'get')
  }
}
