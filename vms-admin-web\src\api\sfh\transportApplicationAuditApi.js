import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/sfh/transportapplicationaudit/` + url, ...arg)

/**
 * 调运审核Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/12/04 10:34
 **/
export default {
  // 获取调运审核分页
  transportApplicationAuditPage(data) {
    return request('page', data, 'get')
  },
  // 添加调运审核
  transportApplicationAuditSubmitForm(data) {
    return request('add', data, 'post')
  },
  // 删除调运审核
  transportApplicationAuditDelete(data) {
    return request('delete', data)
  },
  // 获取调运审核详情
  transportApplicationAuditDetail(data) {
    return request('detail', data, 'get')
  },
  // 作废调运审核
  transportApplicationVoided(data) {
    return request('voided', data, 'post')
  }
}
