<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <script src="./jessibuca.js"></script>
    <style>
        .root {
            display: flex;
            place-content: center;
            margin-top: 3rem;
        }

        .container-shell {
            backdrop-filter: blur(5px);
            background: hsla(0, 0%, 50%, 0.5);
            padding: 30px 4px 10px 4px;
            /* border: 2px solid black; */
            width: auto;
            position: relative;
            border-radius: 5px;
            box-shadow: 0 10px 20px;
        }

        .container-shell:before {
            content: "jessibuca demo player";
            position: absolute;
            color: darkgray;
            top: 4px;
            left: 10px;
            text-shadow: 1px 1px black;
        }

        #container {
            background: rgba(13, 14, 27, 0.7);
            width: 640px;
            height: 398px;
        }

        .input {
            display: flex;
            margin-top: 10px;
            color: white;
            place-content: stretch;
        }

        .input2 {
            bottom: 0px;
        }

        .input input {
            flex: auto;
        }

        .err {
            position: absolute;
            top: 40px;
            left: 10px;
            color: red;
        }

        .option {
            position: absolute;
            top: 4px;
            right: 10px;
            display: flex;
            place-content: center;
            font-size: 12px;
        }

        .option span {
            color: white;
        }

        .page {
            background: url('./bg.jpg');
            background-repeat: no-repeat;
            background-position: top;
        }

        @media (max-width: 720px) {
            #container {
                width: 90vw;
                height: 52.7vw;
            }
        }
    </style>
</head>
<body class="page">
<div class="root">
    <div class="container-shell">
        <div id="container"></div>
        <div class="input">
            <div>输入URL：</div>
            <input
                autocomplete="on"
                id="playUrl"
                value=""
            />
            <button id="play">播放</button>
            <button id="pause" style="display: none">停止</button>
        </div>
        <div class="input" style="line-height: 30px">
            <button id="destroy">销毁</button>
        </div>
    </div>
</div>

<script>
    var $player = document.getElementById('play');
    var $pause = document.getElementById('pause');
    var $playHref = document.getElementById('playUrl');
    var $container = document.getElementById('container');
    var $destroy = document.getElementById('destroy');

    var showOperateBtns = false; // 是否显示按钮
    var forceNoOffscreen = true; //
    var jessibuca = null;

    function create() {
        jessibuca = null;
        jessibuca = new Jessibuca({
            container: $container,
            videoBuffer: 0.2, // 缓存时长
            isResize: false,
            text: "",
            loadingText: "",
            useMSE: false,
            debug: true,
            showBandwidth: showOperateBtns, // 显示网速
            operateBtns: {
                fullscreen: showOperateBtns,
                screenshot: showOperateBtns,
                play: showOperateBtns,
                audio: false,
                recorder: false
            },
            forceNoOffscreen: forceNoOffscreen,
            isNotMute: false,
        },);

        jessibuca.on('audioInfo', function (audioInfo) {
            console.log('audioInfo',audioInfo);
        })

        jessibuca.on('videoInfo', function (videoInfo) {
            console.log('videoInfo',videoInfo);
        })

        $player.style.display = 'inline-block';
        $pause.style.display = 'none';
        $destroy.style.display = 'none';
    }


    create();

    $player.addEventListener('click', function () {
        var href = $playHref.value;
        if (href) {
            jessibuca.play(href);
            $player.style.display = 'none';
            $pause.style.display = 'inline-block';
            $destroy.style.display = 'inline-block';
        }
    }, false)


    $pause.addEventListener('click', function () {
        $player.style.display = 'inline-block';
        $pause.style.display = 'none';
        jessibuca.pause();
    })

    $destroy.addEventListener('click', function () {
        if (jessibuca) {
            jessibuca.destroy().then(()=>{
                create();
            });
        }
        else {
            create();
        }
    })

</script>

</body>
</html>

