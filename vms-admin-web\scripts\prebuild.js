const fs = require('fs')
const path = require('path')

// 源目录和目标目录
const sourceDir = path.resolve(__dirname, '../src/override/snowy-form-design')
const targetDir = path.resolve(__dirname, '../node_modules/snowy-form-design/dist')
const viteCacheDir = path.resolve(__dirname, '../node_modules/.vite')

// 拷贝文件
function copyDir(src, dest) {
  if (!fs.existsSync(src)) {
    console.error(`Source directory "${src}" does not exist.`)
    process.exit(1)
  }

  console.log(`Copying files from "${src}" to "${dest}"...`)

  // 创建目标目录
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true })
    console.log(`Created directory: ${dest}`)
  }

  const entries = fs.readdirSync(src, { withFileTypes: true })

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name)
    const destPath = path.join(dest, entry.name)

    if (entry.isDirectory()) {
      copyDir(srcPath, destPath)
    } else {
      fs.copyFileSync(srcPath, destPath)
      console.log(`Copied file: ${srcPath} -> ${destPath}`)
    }
  }

  console.log('Copy operation completed.')
}

// 删除文件或目录
function deleteDir(dir) {
  if (fs.existsSync(dir)) {
    console.log(`Deleting directory: ${dir}`)
    fs.rmSync(dir, { recursive: true, force: true })
    console.log('Delete operation completed.')
  } else {
    console.log(`Directory "${dir}" does not exist. Skipping delete operation.`)
  }
}

try {
  console.log('Prebuild script started.')

  // 拷贝文件
  copyDir(sourceDir, targetDir)

  // 删除 Vite 缓存目录
  deleteDir(viteCacheDir)

  console.log('Prebuild script executed successfully.')
} catch (error) {
  console.error('Error during prebuild:', error)
  process.exit(1)
}
