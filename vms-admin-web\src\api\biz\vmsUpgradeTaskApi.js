import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/upgradetask/` + url, ...arg)

/**
 * 设备升级任务表Api接口管理器
 *
 * <AUTHOR>
 * @date  2025/03/04 19:27
 **/
export default {
  // 获取设备升级任务表分页
  vmsUpgradeTaskPage(data) {
    return request('page', data, 'get')
  },
  // 提交设备升级任务表表单 edit为true时为编辑，默认为新增
  vmsUpgradeTaskSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除设备升级任务表
  vmsUpgradeTaskDelete(data) {
    return request('delete', data)
  },
  // 获取设备升级任务表详情
  vmsUpgradeTaskDetail(data) {
    return request('detail', data, 'get')
  },
  // 操作升级任务
  vmsUpgradeTaskOpera(data) {
    return request('opera', data, 'post')
  }
}
