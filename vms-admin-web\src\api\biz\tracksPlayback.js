import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/route/` + url, ...arg)

/**
 * 监控中心
 *
 * <AUTHOR>
 * @date  2024/07/08
 **/
export default {
  // 根据时间范围查询到的车辆行程汇总
  summary(data) {
    return request('summary', data, 'get')
  },

  // 获取行程列表
  list(data) {
    return request('list', data, 'get')
  },

  // 获取行程位置详情
  getLocationDetail(data) {
    return request('getLocationDetail', data, 'get')
  },

  // 获取行程位置详情
  // 	id	行程ID
  // fields	需要查询的字段列表
  // coordinate	坐标系.0-wgs84,1-gcj02,2-bd09
  getAlarmDetail(data) {
    return request('getAlarmDetail', data, 'get')
  },

  // 根据年月查询按日汇总的车辆行程数据
  dailySummary(data) {
    return request('dailySummary', data, 'get')
  }
}
