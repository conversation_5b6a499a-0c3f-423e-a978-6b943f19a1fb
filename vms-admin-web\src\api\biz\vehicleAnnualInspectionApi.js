import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/vehicleannualinspection/` + url, ...arg)

/**
 * 车辆年检Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/29 14:29
 **/
export default {
  // 获取车辆年检分页
  vehicleAnnualInspectionPage(data) {
    return request('page', data, 'get')
  },
  // 提交车辆年检表单 edit为true时为编辑，默认为新增
  vehicleAnnualInspectionSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除车辆年检
  vehicleAnnualInspectionDelete(data) {
    return request('delete', data)
  },
  // 获取车辆年检详情
  vehicleAnnualInspectionDetail(data) {
    return request('detail', data, 'get')
  },
  // 导出车辆年检列表
  export(data) {
    return request('export', data, 'get', {
      responseType: 'blob'
    })
  }
}
