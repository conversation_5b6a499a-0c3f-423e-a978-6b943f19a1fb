import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/sfh/enterprisedeviceregistration/` + url, ...arg)

/**
 * 设备信息备案表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/12/10 14:51
 **/
export default {
  // 获取设备信息备案表分页
  enterpriseDeviceRegistrationPage(data) {
    return request('page', data, 'get')
  },
  // 提交设备信息备案表表单 edit为true时为编辑，默认为新增
  enterpriseDeviceRegistrationSubmitForm(data) {
    return request('add', data)
  },
  // 删除设备信息备案表
  enterpriseDeviceRegistrationDelete(data) {
    return request('delete', data)
  },
  // 获取设备信息备案表详情
  enterpriseDeviceRegistrationDetail(data) {
    return request('detail', data, 'get')
  }
}
