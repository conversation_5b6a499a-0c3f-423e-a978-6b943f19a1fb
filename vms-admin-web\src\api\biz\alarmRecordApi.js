import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/alarmrecord/` + url, ...arg)

/**
 * 报警统计Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/09/02 19:59
 **/
export default {
  // 获取报警统计分页
  alarmRecordPage(data) {
    return request('page', data, 'get')
  },
  // 提交报警统计表单 edit为true时为编辑，默认为新增
  alarmRecordSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除报警统计
  alarmRecordDelete(data) {
    return request('delete', data)
  },
  // 获取报警统计详情
  alarmRecordDetail(data) {
    return request('detail', data, 'get')
  },
  // 处理报警
  processAlarmRecord(data) {
    return request('process', data, 'post')
  },
  // 获取报警统计分页
  alarmRecordCount(data) {
    return request('count', data, 'get')
  },
  // 获取报警统计分页
  exportAlarmRecord(data) {
    return request('export', data, 'get', {
      responseType: 'blob'
    })
  }
}
