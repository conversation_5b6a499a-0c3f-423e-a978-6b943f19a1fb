import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/sfh/routepoint/` + url, ...arg)

/**
 * 运输路线起始点信息Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/12/13 14:04
 **/
export default {
  // 获取运输路线起始点信息分页
  routePointPage(data) {
    return request('page', data, 'get')
  },
  // 提交运输路线起始点信息表单 edit为true时为编辑，默认为新增
  routePointSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除运输路线起始点信息
  routePointDelete(data) {
    return request('delete', data)
  },
  // 获取运输路线起始点信息详情
  routePointDetail(data) {
    return request('detail', data, 'get')
  },
  // 获取运输路线起始点信息列表
  routePointList(data) {
    return request('list', data, 'get')
  }
}
