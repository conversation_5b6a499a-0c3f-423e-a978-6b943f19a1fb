import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/route/` + url, ...arg)

/**
 * 行程表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/10/16 08:39
 **/
export default {
  // 获取行程表分页
  routePage(data) {
    return request('page', data, 'get')
  },
  // 提交行程表表单 edit为true时为编辑，默认为新增
  routeSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除行程表
  routeDelete(data) {
    return request('delete', data)
  },
  // 获取行程表详情
  routeDetail(data) {
    return request('detail', data, 'get')
  },
  // 导出车辆行程列表
  routeExport(data) {
    return request('export', data, 'get', {
      responseType: 'blob'
    })
  },
}
