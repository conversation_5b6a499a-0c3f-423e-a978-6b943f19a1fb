import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/flw/process/monitor/` + url, ...arg)
/**
 * 流程
 *
 * <AUTHOR>
 * @date 2022-09-22 22:33:20
 */
export default {
  // 获取所有流程分页
  processMonitorPage(data) {
    return request('monitorPage', data, 'get')
  },
  // 删除流程
  processDelete(data) {
    return request('delete', data)
  },
  // 终止流程
  processEnd(data) {
    return request('end', data)
  },
  // 撤回流程
  processRevoke(data) {
    return request('revoke', data)
  },
  // 挂起流程
  processSuspend(data) {
    return request('suspend', data)
  },
  // 激活流程
  processActive(data) {
    return request('active', data)
  },
  // 转办流程
  processTurn(data) {
    return request('turn', data)
  },
  // 跳转流程
  processJump(data) {
    return request('jump', data)
  },
  // 复活流程
  processRestart(data) {
    return request('restart', data)
  },
  // 迁移流程
  processMigrate(data) {
    return request('migrate', data)
  },
  // 获取流程变量分页
  processVariablePage(data) {
    return request('variablePage', data, 'get')
  },
  // 批量编辑流程变量
  processVariableUpdateBatch(data) {
    return request('variableUpdateBatch', data)
  },
  // 获取流程详情
  processDetail(data) {
    return request('detail', data, 'get')
  },
  // 获取可跳转节点列表
  processGetCanJumpNodeInfoList(data) {
    return request('getCanJumpNodeInfoList', data, 'get')
  },
  // 获取可复活到节点列表
  processGetCanRestartNodeInfoList(data) {
    return request('getCanRestartNodeInfoList', data, 'get')
  },
  // 获取可迁移到节点列表
  processGetCanMigrateNodeInfoList(data) {
    return request('getCanMigrateNodeInfoList', data, 'get')
  },
  // 获取组织树选择器
  processOrgTreeSelector(data) {
    return request('orgTreeSelector', data, 'get')
  },
  // 获取用户选择器
  processUserSelector(data) {
    return request('userSelector', data, 'get')
  },
  // 获取模型详情
  processModelDetail(data) {
    return request('modelDetail', data, 'get')
  },
  // 获取司机选择器
  processDriverSelector(data) {
    return request('driverSelector', data, 'get')
  },
  // 获取车辆选择器
  processVehicleSelector(data) {
    return request('vehicleSelector', data, 'get')
  }
}
