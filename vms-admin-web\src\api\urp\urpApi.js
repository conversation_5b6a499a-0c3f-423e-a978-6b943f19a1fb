import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/urp/storage/` + url, ...arg)

export default {
  // 获取报表分页
  urpPage(data) {
    return request('page', data, 'get')
  },
  // 提交表单 edit为true时为编辑，默认为新增
  urpSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除报表
  urpDelete(data) {
    return request('delete', data)
  },
  // 获取报表详情
  urpDetail(data) {
    return request('detail', data, 'get')
  }
}
