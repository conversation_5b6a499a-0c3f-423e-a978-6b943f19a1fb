import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/monitor/` + url, ...arg)

/**
 * 监控中心
 *
 * <AUTHOR>
 * @date  2024/07/08
 **/
export default {
  // 获取组织车辆树
  getOrgVehicleTree(data) {
    return request('vehicle/tree', data, 'get')
  },
  // 获取组织设备树
  getOrgDeviceTree(data) {
    return request('tree', data, 'get')
  },
  // 获取组织司机树
  getOrgDriverTree(data) {
    return request('driver/tree', data, 'get')
  }
}
