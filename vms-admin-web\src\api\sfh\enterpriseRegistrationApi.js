import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/sfh/enterpriseregistration/` + url, ...arg)

/**
 * 进境粮食企业申请备案Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/11/27 09:27
 **/
export default {
  // 获取进境粮食企业申请备案分页
  enterpriseRegistrationPage(data) {
    return request('page', data, 'get')
  },
  // 提交进境粮食企业申请备案表单 edit为true时为编辑，默认为新增
  enterpriseRegistrationSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除进境粮食企业申请备案
  enterpriseRegistrationDelete(data) {
    return request('delete', data)
  },
  // 获取进境粮食企业申请备案详情
  enterpriseRegistrationDetail(data) {
    return request('detail', data, 'get')
  },
  // 获取进境粮食企业自身申请备案详情
  enterpriseRegistrationGetDetailBySelf(data) {
    return request('getDetailBySelf', data, 'get')
  },
  // 撤回进境粮食企业申请备案
  enterpriseregistrationWithdrawal(data) {
    return request('withdrawal', data, 'post')
  },
  // 提交进境粮食企业申请备案
  enterpriseregistrationSubmit(data) {
    return request('submit', data, 'post')
  },
  // 保存进境粮食企业申请备案
  enterpriseregistrationSave(data) {
    return request('save', data, 'post')
  },
  // 获取进境粮食企业申请备案变更历史列表
  enterpriseRegistrationHistory(data) {
    return request('history', data, 'get')
  },
  // 进境粮食企业申请备案审批
  enterpriseregistrationAudit(data) {
    return request('audit', data, 'post')
  },
  // 获取进境粮食企业申请备案列表
  enterpriseRegistrationList(data) {
    return request('list', data, 'get')
  }
}
