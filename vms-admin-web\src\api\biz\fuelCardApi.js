import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/fuelcard/` + url, ...arg)

/**
 * 油卡信息Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/28 09:42
 **/
export default {
  // 获取油卡信息分页
  fuelCardPage(data) {
    return request('page', data, 'get')
  },
  // 提交油卡信息表单 edit为true时为编辑，默认为新增
  fuelCardSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除油卡信息
  fuelCardDelete(data) {
    return request('delete', data)
  },
  // 获取油卡信息详情
  fuelCardDetail(data) {
    return request('detail', data, 'get')
  },
  // 油卡充值
  fuelCardRecharge(data) {
    return request('recharge', data, 'post')
  },
  // 导出油卡信息列表
  export(data) {
    return request('export', data, 'get', {
      responseType: 'blob'
    })
  }
}
