import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/rawdata/` + url, ...arg)

/**
 * 设备上报原始数据表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/10/22 14:00
 **/
export default {
  // 获取设备上报原始数据表分页
  jt808RawDataPage(data) {
    return request('page', data, 'get')
  },
  // 提交设备上报原始数据表表单 edit为true时为编辑，默认为新增
  jt808RawDataSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除设备上报原始数据表
  jt808RawDataDelete(data) {
    return request('delete', data)
  },
  // 获取设备上报原始数据表详情
  jt808RawDataDetail(data) {
    return request('detail', data, 'get')
  },
  rawdataExport(data) {
    return request('export', data, 'get', {
      responseType: 'blob'
    })
  },
  // 获取设备上报原始数据表详情
  jt808RawDataDecode(data) {
    return request('decode', data, 'post')
  }
}
