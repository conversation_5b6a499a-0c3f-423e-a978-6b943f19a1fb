import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/upgradeversion/` + url, ...arg)

/**
 * 设备升级版本表Api接口管理器
 *
 * <AUTHOR>
 * @date  2025/03/04 19:25
 **/
export default {
  // 获取设备升级版本表分页
  vmsUpgradeVersionPage(data) {
    return request('page', data, 'get')
  },
  // 获取设备升级版本表列表
  vmsUpgradeVersionList(data) {
    return request('list', data, 'get')
  },
  // 提交设备升级版本表表单 edit为true时为编辑，默认为新增
  vmsUpgradeVersionSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除设备升级版本表
  vmsUpgradeVersionDelete(data) {
    return request('delete', data)
  },
  // 获取设备升级版本表详情
  vmsUpgradeVersionDetail(data) {
    return request('detail', data, 'get')
  }
}
