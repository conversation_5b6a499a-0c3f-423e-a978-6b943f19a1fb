import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/vehiclemaintenance/` + url, ...arg)

/**
 * 车辆保养Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/29 14:31
 **/
export default {
  // 获取车辆保养分页
  vehicleMaintenancePage(data) {
    return request('page', data, 'get')
  },
  // 提交车辆保养表单 edit为true时为编辑，默认为新增
  vehicleMaintenanceSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除车辆保养
  vehicleMaintenanceDelete(data) {
    return request('delete', data)
  },
  // 获取车辆保养详情
  vehicleMaintenanceDetail(data) {
    return request('detail', data, 'get')
  },
  // 导出车辆保养列表
  export(data) {
    return request('export', data, 'get', {
      responseType: 'blob'
    })
  }
}
