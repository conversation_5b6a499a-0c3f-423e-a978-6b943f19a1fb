import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/instruction/` + url, ...arg)
/**
 * 指令下发接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/28
 **/
export default {
  // 下发设置打印串口日志指令
  serialPortLog(data) {
    return request('serialPortLog', data, 'post')
  },
  // 下发设备重启指令
  restart(data) {
    return request('restart', data, 'post')
  },
  // 下发设置车牌号码指令
  plateNumber(data) {
    return request('plateNumber', data, 'post')
  },
  // 下发设置超速阀值指令
  overSpeed(data) {
    return request('overSpeed', data, 'post')
  },
  // 下发设置熄火回传间隔指令
  offInterval(data) {
    return request('offInterval', data, 'post')
  },
  // 下发设置开关OBD指令
  obd(data) {
    return request('obd', data, 'post')
  },
  // 下发设置初始化里程油耗指令
  mileageFuel(data) {
    return request('mileageFuel', data, 'post')
  },
  // 下发设置低电报警阀值指令
  lowBattery(data) {
    return request('lowBattery', data, 'post')
  },
  // 下发设置回传间隔指令
  interval(data) {
    return request('interval', data, 'post')
  },
  // 下发设置点火阈值指令
  ignition(data) {
    return request('ignition', data, 'post')
  },
  // 下发设置心跳数据间隔指令
  heartbeat(data) {
    return request('heartbeat', data, 'post')
  },
  // 下发设置GPRS指令
  gprs(data) {
    return request('gprs', data, 'post')
  },
  // 下发设置疲劳驾驶时长指令
  fatigueDriving(data) {
    return request('fatigueDriving', data, 'post')
  },
  // 下发设置APN指令
  apn(data) {
    return request('apn', data, 'post')
  },
  // 下发设置纯文本指令
  text(data) {
    return request('text', data, 'post')
  }
}
