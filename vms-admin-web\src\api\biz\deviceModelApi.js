import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/devicemodel/` + url, ...arg)

/**
 * 设备型号Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/26 19:42
 **/
export default {
  // 获取设备型号分页
  deviceModelPage(data) {
    return request('page', data, 'get')
  },
  deviceModelList(data) {
    return request('list', data, 'get')
  },
  deviceModelBasicList(data) {
    return request('basicList', data, 'get')
  },
  // 提交设备型号表单 edit为true时为编辑，默认为新增
  deviceModelEdit(data) {
    return request('edit', data)
  },
  deviceModelAdd(data) {
    return request('add', data)
  },
  // 删除设备型号
  deviceModelDelete(data) {
    return request('delete', data)
  },
  // 获取设备型号详情
  deviceModelDetail(data) {
    return request('detail', data, 'get')
  }
}
