import {
  baseRequest
} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/eventrecord/` + url, ...arg)

/**
 * 事件记录表Api接口管理器
 *
 * <AUTHOR>
 * @date  2025/08/22 08:43
 **/
export default {
  // 获取事件记录表分页
  eventRecordPage(data) {
    return request('page', data, 'get')
  },
  // 提交事件记录表表单 edit为true时为编辑，默认为新增
  eventRecordSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除事件记录表
  eventRecordDelete(data) {
    return request('delete', data)
  },
  // 获取事件记录表详情
  eventRecordDetail(data) {
    return request('detail', data, 'get')
  },
  // 导出事件记录
  exportEventRecord(data) {
    return request('export', data, 'get', {
      responseType: 'blob'
    })
  }
}
