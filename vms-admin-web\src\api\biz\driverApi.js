import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/driver/` + url, ...arg)

/**
 * 司机Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/29 14:14
 **/
export default {
  // 获取司机分页
  driverPage(data) {
    return request('page', data, 'get')
  },
  // 提交司机表单 edit为true时为编辑，默认为新增
  driverSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除司机
  driverDelete(data) {
    return request('delete', data)
  },
  // 获取司机详情
  driverDetail(data) {
    return request('detail', data, 'get')
  },

  // 司机绑定车辆
  driverBindVehicle(data) {
    return request('bindVehicle', data, 'post')
  },
  // 司机解绑车辆
  driverUnbindVehicle(data) {
    return request('unbindVehicle', data, 'post')
  },
  // 根据id集合获取车辆集合
  getDriverListByIdList(data) {
    return request('getDriverListByIdList', data)
  },
  // 重置司机密码
  resetPasswords(data) {
    return request('password/reset', data, 'post')
  },
  // 下载司机导入模板
  downloadImportDriverTemplate(data) {
    return request('downloadImportDriverTemplate', data, 'get', {
      responseType: 'blob'
    })
  },
  // 司机导入
  importDriver(data) {
    return request('import', data, 'post')
  },
  // 司机导出
  exportDriver(data) {
    return request('export', data, 'get', {
      responseType: 'blob'
    })
  },
  // 获取司机绑定历史
  getDriverBindHistory(data) {
    return request('bindHistory', data, 'get')
  }
}
