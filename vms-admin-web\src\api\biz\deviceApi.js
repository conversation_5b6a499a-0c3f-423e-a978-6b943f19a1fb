import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/device/` + url, ...arg)

/**
 * 设备信息Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/29 10:33
 **/
export default {
  // 获取设备信息分页
  devicePage(data) {
    return request('page', data, 'get')
  },
  // 提交设备信息表单 edit为true时为编辑，默认为新增
  deviceSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除设备信息
  deviceDelete(data) {
    return request('delete', data)
  },
  // 获取设备信息详情
  deviceDetail(data) {
    return request('detail', data, 'get')
  },
  //批量转移设备组织
  batchMove(data) {
    return request('batchMove', data, 'post')
  },
  //下载设备导入模板
  downloadImportDeviceTemplate(data) {
    return request('downloadImportDeviceTemplate', data, 'get', {
      responseType: 'blob'
    })
  },
  //设备导入
  import(data) {
    return request('import', data, 'post')
  },
  //设备导出
  export(data) {
    return request('export', data, 'get', {
      responseType: 'blob'
    })
  },
  // 获取设备绑定历史
  getDeviceBindingHistory(data) {
    return request('bindHistory', data, 'get')
  }
}
