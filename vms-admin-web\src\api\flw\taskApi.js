import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/flw/task/` + url, ...arg)
/**
 * 待办任务
 *
 * <AUTHOR>
 * @date 2022-09-22 22:33:20
 */
export default {
  // 获取待办任务分页
  taskTodoPage(data) {
    return request('todoPage', data, 'get')
  },
  // 获取已办任务分页
  taskDonePage(data) {
    return request('donePage', data, 'get')
  },
  // 调整申请
  taskAdjust(data) {
    return request('adjust', data)
  },
  // 审批保存
  taskSave(data) {
    return request('save', data)
  },
  // 审批同意
  taskPass(data) {
    return request('pass', data)
  },
  // 审批拒绝
  taskReject(data) {
    return request('reject', data)
  },
  // 审批退回
  taskBack(data) {
    return request('back', data)
  },
  // 任务转办
  taskTurn(data) {
    return request('turn', data)
  },
  // 审批跳转
  taskJump(data) {
    return request('jump', data)
  },
  // 任务加签
  taskAddSign(data) {
    return request('addSign', data)
  },
  // 任务详情
  taskDetail(data) {
    return request('detail', data, 'get')
  },
  // 获取可驳回节点列表
  taskGetCanBackNodeInfoList(data) {
    return request('getCanBackNodeInfoList', data, 'get')
  },
  // 获取可跳转节点列表
  taskGetCanJumpNodeInfoList(data) {
    return request('getCanJumpNodeInfoList', data, 'get')
  },
  // 获取组织树选择器
  taskOrgTreeSelector(data) {
    return request('orgTreeSelector', data, 'get')
  },
  // 获取用户选择器
  taskUserSelector(data) {
    return request('userSelector', data, 'get')
  },
  // 获取司机选择器
  taskDriverSelector(data) {
    return request('driverSelector', data, 'get')
  },
  // 获取车辆选择器
  taskVehicleSelector(data) {
    return request('vehicleSelector', data, 'get')
  },
  // 获取机构选择器
  taskOrgSelector(data) {
    return request('orgSelector', data, 'get')
  },
  // 获取职位选择器
  taskPositionSelector(data) {
    return request('positionSelector', data, 'get')
  },
  // 获取角色选择器
  taskRoleSelector(data) {
    return request('roleSelector', data, 'get')
  },
  // 获取下一节点信息集合
  taskGetNextNodeInfo(data) {
    return request('getNextNodeInfo', data)
  },
  // 获取模型详情
  taskModelDetail(data) {
    return request('modelDetail', data, 'get')
  }
}
