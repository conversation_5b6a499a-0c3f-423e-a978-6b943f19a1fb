import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/vehicleinsurance/` + url, ...arg)

/**
 * 车辆保险Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/29 14:30
 **/
export default {
  // 获取车辆保险分页
  vehicleInsurancePage(data) {
    return request('page', data, 'get')
  },
  // 提交车辆保险表单 edit为true时为编辑，默认为新增
  vehicleInsuranceSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除车辆保险
  vehicleInsuranceDelete(data) {
    return request('delete', data)
  },
  // 获取车辆保险详情
  vehicleInsuranceDetail(data) {
    return request('detail', data, 'get')
  },
  // 导出车辆保险列表
  export(data) {
    return request('export', data, 'get', {
      responseType: 'blob'
    })
  }
}
