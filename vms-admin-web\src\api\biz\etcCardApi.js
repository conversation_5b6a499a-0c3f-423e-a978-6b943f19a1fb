import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/etccard/` + url, ...arg)

/**
 * ETC卡表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/09/26 09:12
 **/
export default {
  // 获取ETC卡表分页
  etcCardPage(data) {
    return request('page', data, 'get')
  },
  // 提交ETC卡表表单 edit为true时为编辑，默认为新增
  etcCardSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除ETC卡表
  etcCardDelete(data) {
    return request('delete', data)
  },
  // 获取ETC卡表详情
  etcCardDetail(data) {
    return request('detail', data, 'get')
  },
  // 导出ETC列表
  export(data) {
    return request('export', data, 'get', {
      responseType: 'blob'
    })
  }
}
