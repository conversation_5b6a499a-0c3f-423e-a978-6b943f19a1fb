import {
  baseRequest
} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/vehicleenergysupplement/` + url, ...arg)

/**
 * 车辆补能数据表Api接口管理器
 *
 * <AUTHOR>
 * @date  2025/08/25 17:08
 **/
export default {
  // 获取车辆补能数据表分页
  vehicleEnergySupplementPage(data) {
    return request('page', data, 'get')
  },
  // 提交车辆补能数据表表单 edit为true时为编辑，默认为新增
  vehicleEnergySupplementSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除车辆补能数据表
  vehicleEnergySupplementDelete(data) {
    return request('delete', data)
  },
  // 获取车辆补能数据表详情
  vehicleEnergySupplementDetail(data) {
    return request('detail', data, 'get')
  },
  //导出
  exportVehicleEnergySupplement(data) {
    return request('export', data, 'get', {
      responseType: 'blob'
    })
  }
}
