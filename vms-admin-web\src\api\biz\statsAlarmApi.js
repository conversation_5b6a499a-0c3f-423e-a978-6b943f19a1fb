import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/stats/alarm/` + url, ...arg)

/**
 * 报警统计Api接口管理器
 *
 * <AUTHOR>
 * @date 2024/07/30 12:00:00
 **/
export default {
  // 获取报警总览统计
  getAlarmOverview(data) {
    return request('overview', data, 'get')
  },
  // 获取报警类型分布统计
  getAlarmTypeDistribution(data) {
    return request('typeDistribution', data, 'get')
  },
  // 获取车辆报警统计
  getVehicleAlarmStats(data) {
    return request('vehicle', data, 'get')
  },
  // 获取司机报警统计
  getDriverAlarmStats(data) {
    return request('driver', data, 'get')
  },
  // 获取报警数据明细列表
  getAlarmRecords(data) {
    return request('records', data, 'get') // 假设后端接口路径为 /biz/stats/alarm/records
  }
}