import {
  baseRequest
} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/devicestatusrecord/` + url, ...arg)

/**
 * devicestatusrecordApi接口管理器
 *
 * <AUTHOR>
 * @date  2025/04/17 08:40
 **/
export default {
  // 获取devicestatusrecord分页
  deviceStatusRecordPage(data) {
    return request('page', data, 'get')
  },
  // 提交devicestatusrecord表单 edit为true时为编辑，默认为新增
  deviceStatusRecordSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除devicestatusrecord
  deviceStatusRecordDelete(data) {
    return request('delete', data)
  },
  // 获取devicestatusrecord详情
  deviceStatusRecordDetail(data) {
    return request('detail', data, 'get')
  },
  //获取停车记录列表
  getParkList(data) {
    return request('park', data, 'get')
  }
}
