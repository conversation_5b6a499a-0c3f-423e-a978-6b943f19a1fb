import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/flw/agent/` + url, ...arg)

/**
 * 流程委托Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/07/26 22:26
 **/
export default {
  // 获取流程我委托的分页
  flwAgentPage(data) {
    return request('page', data, 'get')
  },
  // 获取流程委托给我的分页
  flwAgentMyPage(data) {
    return request('agentPage', data, 'get')
  },
  // 提交流程委托表单 edit为true时为编辑，默认为新增
  flwAgentSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 结束流程委托
  flwAgentEnd(data) {
    return request('endAgent', data)
  },
  // 删除流程委托
  flwAgentDelete(data) {
    return request('delete', data)
  },
  // 获取流程委托详情
  flwAgentDetail(data) {
    return request('detail', data, 'get')
  },
  // 获取组织树
  flwAgentTree(data) {
    return request('orgTree', data, 'get')
  },
  // 获取用户选择器
  flwAgentUserSelector(data) {
    return request('userSelector', data, 'get')
  },
  // 获取用户流程选择器
  flwAgentUserModelSelector(data) {
    return request('userModelSelector', data, 'get')
  }
}
