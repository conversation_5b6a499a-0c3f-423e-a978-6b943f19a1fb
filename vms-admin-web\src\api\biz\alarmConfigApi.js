import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(url, ...arg)
/**
 * 告警规则配置接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/26
 **/
export default {
  // 保存告警规则配置
  save(data) {
    return request('/biz/alarmrules/save', data, 'post')
  },
  // 告警规则配置列表
  getList(data) {
    return request('/biz/alarmrules/list', data, 'get')
  },
  getOptions() {
    return request('/biz/dict/getTenAlarmDict', {}, 'get')
  }
}
