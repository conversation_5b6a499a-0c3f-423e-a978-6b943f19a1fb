import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/dev/dm/` + url, ...arg)

/**
 * 数据库管理Api接口管理器
 *
 * <AUTHOR>
 * @date  2023/08/04 08:18
 **/
export default {
  // 获取所有数据源信息
  dbInfoList(data) {
    return request('dbInfoList', data, 'get')
  },
  // 获取所有表信息
  tables(data) {
    return request('tables', data, 'get')
  },
  // 添加数据库表
  addTable(data) {
    return request('addTable', data)
  },
  // 修改数据库表
  editTable(data) {
    return request('editTable', data)
  },
  // 删除数据库表
  deleteTable(data) {
    return request('deleteTable', data)
  },
  // 获取表内所有字段信息
  columns(data) {
    return request('columns', data, 'get')
  },
  // 添加数据库字段
  addColumn(data) {
    return request('addColumn', data)
  },
  // 修改数据库字段
  editColumn(data) {
    return request('editColumn', data)
  },
  // 删除数据库字段
  deleteColumn(data) {
    return request('deleteColumn', data)
  }
}
