import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/flw/seal/` + url, ...arg)

/**
 * 印章管理Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/04 23:24
 **/
export default {
  // 获取印章管理分页
  flwSealPage(data) {
    return request('page', data, 'get')
  },
  // 提交印章管理表单 edit为true时为编辑，默认为新增
  flwSealSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除印章管理
  flwSealDelete(data) {
    return request('delete', data)
  },
  // 印章设为默认
  flwSetAsDefault(data) {
    return request('setAsDefault', data)
  },
  // 获取印章管理详情
  flwSealDetail(data) {
    return request('detail', data, 'get')
  },
  // 获取印章管理机构树
  flwSealOrgTreeSelector(data) {
    return request('orgTreeSelector', data, 'get')
  }
}
