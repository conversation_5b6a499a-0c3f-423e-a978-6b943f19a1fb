import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/upgradedetail/` + url, ...arg)

/**
 * 设备升级明细表Api接口管理器
 *
 * <AUTHOR>
 * @date  2025/03/04 19:29
 **/
export default {
  // 获取设备升级明细表分页
  vmsUpgradeDetailPage(data) {
    return request('page', data, 'get')
  },
  // 提交设备升级明细表表单 edit为true时为编辑，默认为新增
  vmsUpgradeDetailSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除设备升级明细表
  vmsUpgradeDetailDelete(data) {
    return request('delete', data)
  },
  // 获取设备升级明细表详情
  vmsUpgradeDetailDetail(data) {
    return request('detail', data, 'get')
  }
}
