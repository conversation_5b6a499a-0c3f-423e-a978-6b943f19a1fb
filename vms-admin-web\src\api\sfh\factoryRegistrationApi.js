import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/sfh/factoryregistration/` + url, ...arg)

/**
 * 加工企业申请备案Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/11/27 09:52
 **/
export default {
  // 获取加工企业申请备案分页
  factoryRegistrationPage(data) {
    return request('page', data, 'get')
  },
  // 提交加工企业申请备案表单 edit为true时为编辑，默认为新增
  factoryRegistrationSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除加工企业申请备案
  factoryRegistrationDelete(data) {
    return request('delete', data)
  },
  // 获取加工企业申请备案详情
  factoryRegistrationDetail(data) {
    return request('detail', data, 'get')
  },
  // 撤回加工企业申请备案
  factoryregistrationWithdrawal(data) {
    return request('withdrawal', data, 'post')
  },
  // 提交加工企业申请备案
  factoryregistrationSubmit(data) {
    return request('submit', data, 'post')
  },
  // 保存加工企业申请备案
  factoryregistrationSave(data) {
    return request('save', data, 'post')
  },
  // 审核加工企业申请备案
  factoryregistrationAudit(data) {
    return request('audit', data, 'post')
  },
  // 获取加工企业自身的申请备案详情
  factoryregistrationSelfDetail(data) {
    return request('self', data, 'get')
  },
  // 获取加工企业申请备案历史
  factoryregistrationHistory(data) {
    return request('history', data, 'get')
  },
  // 获取加工企业列表
  factoryregistrationList(data) {
    return request('list', data, 'get')
  }
}
