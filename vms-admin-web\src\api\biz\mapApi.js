import {
  baseRequest
} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/map/` + url, ...arg)
/**
 * 地理信息接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/26
 **/
export default {
  // 逆地理解析接口
  reverse_geocoding(lon, lat, coordinate) {
    return request('reverse_geocoding', {
      lon,
      lat,
      coordinate
    }, 'get')
  },
  // 区域搜索接口
  region_search(data) {
    return request('region_search', data, 'get')
  },
  // 行政区域边界查询接口
  region_boundary(data) {
    return request('region_boundary', data, 'get')
  },
  // 地理编码接口
  geocoding(data) {
    return request('geocoding', data, 'get')
  },
  // 驾车路线规划
  driverRoutePlanning(data) {
    return request('drivingRoutePlanning', data, 'get')
  },
  //行政区域查询接口
  districts(data) {
    return request('districts', data, 'get')
  },
  //高德输入提示查询接口
  inputTips(data) {
    return request('inputTips', data, 'get')
  },
  //高德POI查询接口
  pois(data) {
    return request('pois', data, 'get')
  }
}
