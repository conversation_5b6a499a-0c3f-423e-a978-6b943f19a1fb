import {baseRequest} from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/fuelcardrecord/` + url, ...arg)

/**
 * 油卡记录表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/28 14:44
 **/
export default {
  // 获取油卡记录表分页
  fuelCardRecordPage(data) {
    return request('page', data, 'get')
  },
  // 提交油卡记录表表单 edit为true时为编辑，默认为新增
  fuelCardRecordSubmitForm(data, edit = false) {
    return request(edit ? 'edit' : 'add', data)
  },
  // 删除油卡记录表
  fuelCardRecordDelete(data) {
    return request('delete', data)
  },
  // 获取油卡记录表详情
  fuelCardRecordDetail(data) {
    return request('detail', data, 'get')
  },
  // 导出油卡记录列表
  export(data) {
    return request('export', data, 'get', {
      responseType: 'blob'
    })
  }
}
